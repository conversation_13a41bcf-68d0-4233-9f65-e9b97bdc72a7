import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsArray, 
  IsEnum, 
  IsNumber, 
  IsObject, 
  IsOptional, 
  IsString, 
  MaxLength, 
  Min, 
  ValidateNested 
} from 'class-validator';
import { ProviderEnum } from '../../../constants';
import { 
  InputModalityEnum, 
  OutputModalityEnum, 
  SamplingParameterEnum, 
  FeatureEnum 
} from '../../../constants/model-capabilities.enum';
import { ModelPricingDto } from './create-model-registry.dto';

/**
 * DTO cho việc cập nhật model registry
 */
export class UpdateModelRegistryDto {

  /**
   * Tên mẫu đại diện của model
   */
  @ApiPropertyOptional({
    description: 'Tên mẫu đại diện của model (phải unique)',
    example: 'gpt-4*',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  modelNamePattern?: string;

  /**
   * C<PERSON>c loại dữ liệu đầu vào hỗ trợ (text, image, audio,...)
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(InputModalityEnum, { each: true })
  inputModalities?: InputModalityEnum[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OutputModalityEnum, { each: true })
  outputModalities?: OutputModalityEnum[];

  /**
   * Các tham số sampling như temperature, top_p,...
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling hỗ trợ',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(SamplingParameterEnum, { each: true })
  samplingParameters?: SamplingParameterEnum[];

  /**
   * Tập hợp feature đặc biệt (như tool-use, function-calling)
   */
  @ApiPropertyOptional({
    description: 'Các feature đặc biệt hỗ trợ',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(FeatureEnum, { each: true })
  features?: FeatureEnum[];

  /**
   * Giá cơ bản cho model (input/output rate)
   */
  @ApiPropertyOptional({
    description: 'Giá cơ bản cho model',
    type: ModelPricingDto,
    example: { inputRate: 1, outputRate: 2 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelPricingDto)
  basePricing?: ModelPricingDto;

  /**
   * Giá fine-tune cho model (input/output rate)
   */
  @ApiPropertyOptional({
    description: 'Giá fine-tune cho model',
    type: ModelPricingDto,
    example: { inputRate: 2, outputRate: 4 },
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ModelPricingDto)
  fineTunePricing?: ModelPricingDto;

  /**
   * Giá training cho model
   */
  @ApiPropertyOptional({
    description: 'Giá training cho model',
    example: 0,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  trainingPricing?: number;
}
