import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response';
import { ModelRegistryRepository } from '../../repositories/model-registry.repository';
import { ModelRegistryException } from '../../exceptions/model-registry.exception';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import {
  CreateBulkModelRegistryDto,
  UpdateModelRegistryDto,
  ModelRegistryQueryDto,
  ModelRegistryResponseDto,
  ModelRegistryDetailResponseDto,
  BulkDeleteModelRegistryDto,
  BulkDeleteResponseDto
} from '../dto/model-registry';


/**
 * Service xử lý logic nghiệp vụ cho Admin Model Registry
 */
@Injectable()
export class AdminModelRegistryService {
  private readonly logger = new Logger(AdminModelRegistryService.name);

  constructor(
    private readonly modelRegistryRepository: ModelRegistryRepository,
    private readonly employeeInfoService: EmployeeInfoService,
  ) {}

  /**
   * Tạo nhiều model registry cùng lúc
   * @param createBulkDto DTO chứa danh sách registry cần tạo
   * @param employeeId ID của employee thực hiện
   * @returns Danh sách registry đã tạo
   */
  @Transactional()
  async bulkCreate(createBulkDto: CreateBulkModelRegistryDto, employeeId: number): Promise<ModelRegistryResponseDto[]> {
    this.logger.log(`Bắt đầu tạo ${createBulkDto.registries.length} model registries bởi employee ${employeeId}`);

    // Kiểm tra trùng lặp pattern trong request
    const patterns = createBulkDto.registries.map(r => r.modelNamePattern);
    const uniquePatterns = [...new Set(patterns)];

    if (patterns.length !== uniquePatterns.length) {
      throw ModelRegistryException.duplicatePatternsInRequest();
    }

    // Kiểm tra trùng lặp pattern với database
    const existingPatterns = await this.modelRegistryRepository.checkDuplicatePatterns(patterns);
    if (existingPatterns.length > 0) {
      throw ModelRegistryException.duplicatePattern(existingPatterns.join(', '));
    }

    try {
      // Tạo registries
      const createdRegistries = await this.modelRegistryRepository.bulkCreate(
        createBulkDto.registries,
        employeeId
      );

      this.logger.log(`Đã tạo thành công ${createdRegistries.length} model registries`);

      // Chuyển đổi sang DTO response
      return createdRegistries.map(registry =>
        plainToInstance(ModelRegistryResponseDto, registry, { excludeExtraneousValues: true })
      );
    } catch (error) {
      this.logger.error('Lỗi khi tạo bulk model registries:', error);
      throw ModelRegistryException.bulkOperationError('Không thể tạo model registries');
    }
  }

  /**
   * Cập nhật một model registry
   * @param id ID của registry cần cập nhật
   * @param updateDto DTO chứa thông tin cập nhật
   * @param employeeId ID của employee thực hiện
   * @returns Registry đã cập nhật
   */
  @Transactional()
  async update(id: string, updateDto: UpdateModelRegistryDto, employeeId: number): Promise<ModelRegistryResponseDto> {
    this.logger.log(`Cập nhật model registry ${id} bởi employee ${employeeId}`);

    // Kiểm tra registry tồn tại
    const existingRegistry = await this.modelRegistryRepository.findById(id);
    if (!existingRegistry) {
      throw ModelRegistryException.notFound(id);
    }

    // Kiểm tra trùng lặp pattern nếu có thay đổi
    if (updateDto.modelNamePattern && updateDto.modelNamePattern !== existingRegistry.modelNamePattern) {
      const isDuplicate = await this.modelRegistryRepository.existsByPattern(updateDto.modelNamePattern, id);
      if (isDuplicate) {
        throw ModelRegistryException.duplicatePattern(updateDto.modelNamePattern);
      }
    }

    try {
      // Cập nhật registry
      await this.modelRegistryRepository.update(id, {
        ...updateDto,
        updatedAt: Date.now(),
        updatedBy: employeeId
      });

      // Lấy registry đã cập nhật
      const updatedRegistry = await this.modelRegistryRepository.findById(id);

      this.logger.log(`Đã cập nhật thành công model registry ${id}`);

      return plainToInstance(ModelRegistryResponseDto, updatedRegistry, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật model registry ${id}:`, error);
      throw ModelRegistryException.validationError('Không thể cập nhật model registry');
    }
  }

  /**
   * Xóa mềm nhiều model registry cùng lúc
   * @param bulkDeleteDto DTO chứa danh sách ID cần xóa
   * @param employeeId ID của employee thực hiện
   * @returns Kết quả xóa
   */
  @Transactional()
  async bulkDelete(bulkDeleteDto: BulkDeleteModelRegistryDto, employeeId: number): Promise<BulkDeleteResponseDto> {
    this.logger.log(`Xóa ${bulkDeleteDto.ids.length} model registries bởi employee ${employeeId}`);

    try {
      const result = await this.modelRegistryRepository.bulkSoftDelete(bulkDeleteDto.ids, employeeId);

      this.logger.log(`Đã xóa thành công ${result.successIds.length}/${bulkDeleteDto.ids.length} model registries`);

      return {
        successIds: result.successIds,
        failedIds: result.failedIds,
        deletedCount: result.successIds.length,
        failedCount: result.failedIds.length
      };
    } catch (error) {
      this.logger.error('Lỗi khi xóa bulk model registries:', error);
      throw ModelRegistryException.bulkOperationError('Không thể xóa model registries');
    }
  }

  /**
   * Lấy danh sách model registry với phân trang
   * @param queryDto DTO query
   * @returns Kết quả phân trang
   */
  async findAll(queryDto: ModelRegistryQueryDto): Promise<PaginatedResult<ModelRegistryResponseDto>> {
    this.logger.log(`Lấy danh sách model registries với query: ${JSON.stringify(queryDto)}`);

    try {
      const result = await this.modelRegistryRepository.findWithPagination(queryDto);

      // Chuyển đổi sang DTO response
      const items = result.items.map(registry =>
        plainToInstance(ModelRegistryResponseDto, registry, { excludeExtraneousValues: true })
      );

      return {
        items,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách model registries:', error);
      throw ModelRegistryException.validationError('Không thể lấy danh sách model registries');
    }
  }

  /**
   * Lấy chi tiết một model registry
   * @param id ID của registry
   * @returns Chi tiết registry
   */
  async findById(id: string): Promise<ModelRegistryDetailResponseDto> {
    this.logger.log(`Lấy chi tiết model registry ${id}`);

    try {
      const registry = await this.modelRegistryRepository.findByIdWithDetails(id);
      if (!registry) {
        throw ModelRegistryException.notFound(id);
      }

      // Lấy thông tin employee
      const [createdEmployee, updatedEmployee] = await Promise.all([
        registry.createdBy ? this.employeeInfoService.getEmployeeInfo(registry.createdBy) : null,
        registry.updatedBy ? this.employeeInfoService.getEmployeeInfo(registry.updatedBy) : null
      ]);

      // Chuyển đổi sang DTO response
      const responseDto = plainToInstance(ModelRegistryDetailResponseDto, registry, { excludeExtraneousValues: true });
      responseDto.created = createdEmployee;
      responseDto.updated = updatedEmployee;

      return responseDto;
    } catch (error) {
      if (error instanceof ModelRegistryException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy chi tiết model registry ${id}:`, error);
      throw ModelRegistryException.validationError('Không thể lấy chi tiết model registry');
    }
  }
}
