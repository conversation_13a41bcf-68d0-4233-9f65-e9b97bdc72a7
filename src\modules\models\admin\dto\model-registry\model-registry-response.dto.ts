import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { ProviderEnum } from '../../../constants';
import { 
  InputModalityEnum, 
  OutputModalityEnum, 
  SamplingParameterEnum, 
  FeatureEnum 
} from '../../../constants/model-capabilities.enum';
import { ModelPricingInterface } from '../../../interfaces/pricing.interface';
import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';

/**
 * DTO cho response của model registry
 */
@Exclude()
export class ModelRegistryResponseDto {
  /**
   * UUID của registry
   */
  @ApiProperty({
    description: 'UUID của registry',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * Nhà cung cấp model
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @Expose()
  provider: ProviderEnum;

  /**
   * Tên mẫu đại diện của model
   */
  @ApiProperty({
    description: 'Tên mẫu đại diện của model',
    example: 'gpt-4*',
  })
  @Expose()
  modelNamePattern: string;

  /**
   * Thời gian tạo (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian tạo (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;

  /**
   * Thời gian cập nhật (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;
}

export class ModelRegistryDetailResponseDto extends ModelRegistryResponseDto {
  
  /**
   * Các loại dữ liệu đầu vào hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
  })
  @Expose()
  inputModalities: InputModalityEnum[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
  })
  @Expose()
  outputModalities: OutputModalityEnum[];

  /**
   * Các tham số sampling hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling hỗ trợ',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
  })
  @Expose()
  samplingParameters: SamplingParameterEnum[];

  /**
   * Các feature đặc biệt hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các feature đặc biệt hỗ trợ',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
  })
  @Expose()
  features: FeatureEnum[];

  /**
   * Giá cơ bản cho model
   */
  @ApiProperty({
    description: 'Giá cơ bản cho model',
    example: { inputRate: 1, outputRate: 2 },
  })
  @Expose()
  basePricing: ModelPricingInterface;

  /**
   * Giá fine-tune cho model
   */
  @ApiProperty({
    description: 'Giá fine-tune cho model',
    example: { inputRate: 2, outputRate: 4 },
  })
  @Expose()
  fineTunePricing: ModelPricingInterface;

  /**
   * Giá training cho model
   */
  @ApiProperty({
    description: 'Giá training cho model',
    example: 0,
  })
  @Expose()
  trainingPricing: number;

  /**
   * Thông tin người tạo
   */
  @ApiProperty({
    description: 'Thông tin người tạo',
    type: EmployeeInfoSimpleDto,
  })
  @Expose()
  created: EmployeeInfoSimpleDto | null;

  /**
   * Thông tin người cập nhật
   */
  @ApiProperty({
    description: 'Thông tin người cập nhật',
    type: EmployeeInfoSimpleDto,
    nullable: true,
  })
  @Expose()
  updated: EmployeeInfoSimpleDto | null;
}
