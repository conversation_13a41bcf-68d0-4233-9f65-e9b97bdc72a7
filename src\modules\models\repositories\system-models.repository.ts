import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { SystemModels } from '../entities/system-models.entity';
import { PaginatedResult } from '@common/response';
import { ProviderEnum } from '../constants';

/**
 * Repository cho SystemModels
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến system models
 */
@Injectable()
export class SystemModelsRepository extends Repository<SystemModels> {
  private readonly logger = new Logger(SystemModelsRepository.name);

  constructor(private dataSource: DataSource) {
    super(SystemModels, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho SystemModels với join model_registry
   * @returns SelectQueryBuilder cho SystemModels với thông tin model registry
   */
  createBaseQuery(): SelectQueryBuilder<SystemModels> {
    return this.createQueryBuilder('systemModels')
      .leftJoin('model_registry', 'registry', 'systemModels.modelRegistryId = registry.id')
      .select([
        'systemModels.id',
        'systemModels.modelId',
        'systemModels.active',
        'registry.provider',
        'registry.modelNamePattern',
        'registry.inputModalities',
        'registry.outputModalities',
        'registry.samplingParameters',
        'registry.features',
        'registry.basePricing',
        'registry.fineTunePricing',
        'registry.trainingPricing'
      ])
      .where('registry.deletedAt IS NULL'); // Chỉ lấy model registry chưa bị xóa
  }

  /**
   * Lấy danh sách system models theo provider với phân trang
   * @param queryDto Query parameters
   * @returns Kết quả phân trang với thông tin model registry
   */
  async findByProviderWithPagination(queryDto: any): Promise<PaginatedResult<any>> {
    const query = this.createBaseQuery();

    // Lọc theo provider
    if (queryDto.provider) {
      query.andWhere('registry.provider = :provider', { provider: queryDto.provider });
    }

    // Lọc theo trạng thái active
    if (queryDto.active !== undefined) {
      query.andWhere('systemModels.active = :active', { active: queryDto.active });
    }

    // Tìm kiếm chung
    if (queryDto.search) {
      query.andWhere(
        '(systemModels.modelId ILIKE :search OR registry.modelNamePattern ILIKE :search)',
        { search: `%${queryDto.search}%` }
      );
    }

    // Sắp xếp
    const sortBy = queryDto.sortBy || 'systemModels.modelId';
    const sortOrder = queryDto.sortOrder || 'ASC';
    query.orderBy(sortBy, sortOrder);

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    // Sử dụng getRawMany để lấy dữ liệu từ join
    const totalItems = await query.getCount();
    const rawResults = await query.getRawMany();

    // Transform raw results to include registry data
    const items = rawResults.map(result => ({
      id: result.systemModels_id,
      modelId: result.systemModels_modelId,
      active: result.systemModels_active,
      provider: result.registry_provider,
      modelNamePattern: result.registry_modelNamePattern,
      inputModalities: result.registry_inputModalities || [],
      outputModalities: result.registry_outputModalities || [],
      samplingParameters: result.registry_samplingParameters || [],
      features: result.registry_features || [],
      basePricing: result.registry_basePricing,
      fineTunePricing: result.registry_fineTunePricing,
      trainingPricing: result.registry_trainingPricing
    }));

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Lấy danh sách system models theo provider (không phân trang)
   * @param provider Nhà cung cấp
   * @returns Danh sách system models với thông tin model registry
   */
  async findByProvider(provider: ProviderEnum): Promise<any[]> {
    const query = this.createBaseQuery()
      .andWhere('registry.provider = :provider', { provider })
      .orderBy('systemModels.modelId', 'ASC');

    const rawResults = await query.getRawMany();

    // Transform raw results
    return rawResults.map(result => ({
      id: result.systemModels_id,
      modelId: result.systemModels_modelId,
      active: result.systemModels_active,
      provider: result.registry_provider,
      modelNamePattern: result.registry_modelNamePattern,
      inputModalities: result.registry_inputModalities || [],
      outputModalities: result.registry_outputModalities || [],
      samplingParameters: result.registry_samplingParameters || [],
      features: result.registry_features || [],
      basePricing: result.registry_basePricing,
      fineTunePricing: result.registry_fineTunePricing,
      trainingPricing: result.registry_trainingPricing
    }));
  }

  /**
   * Kiểm tra system model có tồn tại không
   * @param id ID của system model
   * @returns True nếu tồn tại
   */
  async isExists(id: string): Promise<boolean> {
    const count = await this.createQueryBuilder('systemModels')
      .where('systemModels.id = :id', { id })
      .getCount();

    return count > 0;
  }

  /**
   * Lấy system model theo ID
   * @param id ID của system model
   * @returns System model hoặc null
   */
  async findById(id: string): Promise<SystemModels | null> {
    return this.createQueryBuilder('systemModels')
      .where('systemModels.id = :id', { id })
      .getOne();
  }

  /**
   * Bật/tắt trạng thái active của system model
   * @param id ID của system model
   * @returns System model đã cập nhật
   */
  async toggleActive(id: string): Promise<SystemModels | null> {
    // Lấy trạng thái hiện tại
    const systemModel = await this.findById(id);
    if (!systemModel) {
      return null;
    }

    // Đảo ngược trạng thái active
    const newActiveStatus = !systemModel.active;

    // Cập nhật trạng thái
    await this.createQueryBuilder()
      .update(SystemModels)
      .set({
        active: newActiveStatus,
        updatedAt: Date.now()
      })
      .where('id = :id', { id })
      .execute();

    // Trả về system model đã cập nhật
    return this.findById(id);
  }
}
