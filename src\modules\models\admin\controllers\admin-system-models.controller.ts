import {
  Controller,
  Get,
  Query,
  UseGuards,
  Logger
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import { AdminSystemModelsService } from '../services/admin-system-models.service';
import {
  SystemModelsQueryDto,
  SystemModelsResponseDto
} from '../dto/system-models';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API admin cho System Models
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_SYSTEM_MODELS)
@Controller('admin/system-models')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminSystemModelsController {
  private readonly logger = new Logger(AdminSystemModelsController.name);

  constructor(
    private readonly adminSystemModelsService: AdminSystemModelsService,
  ) {}

  /**
   * API lấy danh sách system models theo provider với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách system models theo provider',
    description: 'Lấy danh sách system models với thông tin đầy đủ từ model registry, có thể lọc theo provider và trạng thái active'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    schema: {
      example: {
        success: true,
        data: {
          items: [
            {
              id: '123e4567-e89b-12d3-a456-426614174000',
              modelId: 'gpt-4-turbo-preview',
              active: true,
              provider: 'OPENAI',
              modelNamePattern: 'gpt-4*',
              inputModalities: ['text', 'image'],
              outputModalities: ['text'],
              samplingParameters: ['temperature', 'top_p'],
              features: ['tool_call'],
              basePricing: { inputRate: 1, outputRate: 2 },
              fineTunePricing: { inputRate: 2, outputRate: 4 },
              trainingPricing: 0
            }
          ],
          meta: {
            totalItems: 100,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 10,
            currentPage: 1
          }
        },
        message: 'Lấy danh sách system models thành công'
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Lỗi validation',
    schema: {
      example: {
        success: false,
        errorCode: 20070,
        message: 'Không thể lấy danh sách system models'
      }
    }
  })
  async findByProvider(
    @Query() queryDto: SystemModelsQueryDto,
  ): Promise<ApiResponseDto<any>> {
    this.logger.log(`Lấy danh sách system models với query: ${JSON.stringify(queryDto)}`);

    const result = await this.adminSystemModelsService.findByProvider(queryDto);
    
    return ApiResponseDto.paginated(result, 'Lấy danh sách system models thành công');
  }

  // TODO: Thêm các endpoints khác khi cần thiết
  // @Post() - Tạo system model mới
  // @Put(':id') - Cập nhật system model
  // @Delete(':id') - Xóa system model
  // @Get(':id') - Lấy chi tiết system model
  // @Patch(':id/toggle-active') - Bật/tắt trạng thái active
}
