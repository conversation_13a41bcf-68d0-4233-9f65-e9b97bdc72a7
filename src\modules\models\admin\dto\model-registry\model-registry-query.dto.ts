import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';
import { ProviderEnum } from '../../../constants';

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum ModelRegistrySortBy {
  PROVIDER = 'provider',
  MODEL_NAME_PATTERN = 'modelNamePattern',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho việc truy vấn danh sách model registry
 */
export class ModelRegistryQueryDto extends QueryDto {
  /**
   * Lọc theo nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnum)
  provider?: ProviderEnum;

  /**
   * T<PERSON><PERSON> kiếm theo model name pattern
   */
  @ApiPropertyOptional({
    description: 'Tì<PERSON> kiếm theo model name pattern',
    example: 'gpt-4',
  })
  @IsOptional()
  @IsString()
  modelNamePattern?: string;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: ModelRegistrySortBy,
    example: ModelRegistrySortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(ModelRegistrySortBy)
  sortBy?: ModelRegistrySortBy = ModelRegistrySortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
