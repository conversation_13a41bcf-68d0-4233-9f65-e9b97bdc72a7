import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { ModelRegistry } from '../entities/model-registry.entity';
import { PaginatedResult } from '@common/response';
import { ProviderEnum } from '../constants';

/**
 * Repository cho ModelRegistry
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến model registry
 */
@Injectable()
export class ModelRegistryRepository extends Repository<ModelRegistry> {
  private readonly logger = new Logger(ModelRegistryRepository.name);

  constructor(private dataSource: DataSource) {
    super(ModelRegistry, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho ModelRegistry
   * @returns SelectQueryBuilder cho ModelRegistry
   */
  createBaseQuery(): SelectQueryBuilder<ModelRegistry> {
    return this.createQueryBuilder('modelRegistry')
      .select([
        'modelRegistry.id',
        'modelRegistry.provider',
        'modelRegistry.modelNamePattern',
        'modelRegistry.inputModalities',
        'modelRegistry.outputModalities',
        'modelRegistry.samplingParameters',
        'modelRegistry.features',
        'modelRegistry.createdAt',
        'modelRegistry.updatedAt',
        'modelRegistry.createdBy',
        'modelRegistry.updatedBy'
      ])
      .where('modelRegistry.deletedAt IS NULL');
  }

  /**
   * Tìm registry theo ID
   * @param id ID của registry
   * @returns Registry hoặc null
   */
  async findById(id: string): Promise<ModelRegistry | null> {
    return this.createBaseQuery()
      .where('modelRegistry.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm registry theo provider
   * @param provider Nhà cung cấp
   * @returns Danh sách registry
   */
  async findByProvider(provider: ProviderEnum): Promise<ModelRegistry[]> {
    return this.createBaseQuery()
      .where('modelRegistry.provider = :provider', { provider })
      .orderBy('modelRegistry.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Kiểm tra pattern đã tồn tại chưa
   * @param pattern Pattern cần kiểm tra
   * @param excludeId ID cần loại trừ (cho update)
   * @returns True nếu đã tồn tại
   */
  async isPatternExists(pattern: string, excludeId?: string): Promise<boolean> {
    const query = this.createQueryBuilder('modelRegistry')
      .where('modelRegistry.modelNamePattern = :pattern', { pattern });

    if (excludeId) {
      query.andWhere('modelRegistry.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Tìm registry với phân trang
   * @param queryDto Query parameters
   * @returns Kết quả phân trang
   */
  async findWithPagination(queryDto: any): Promise<PaginatedResult<ModelRegistry>> {
    const query = this.createBaseQuery();

    // Tìm kiếm theo model name pattern
    if (queryDto.modelNamePattern) {
      query.andWhere('modelRegistry.modelNamePattern ILIKE :pattern', {
        pattern: `%${queryDto.modelNamePattern}%`
      });
    }

    // Tìm kiếm theo feature
    if (queryDto.feature) {
      query.andWhere('modelRegistry.features @> :feature', {
        feature: JSON.stringify([queryDto.feature])
      });
    }

    // Tìm kiếm chung
    if (queryDto.search) {
      query.andWhere(
        '(modelRegistry.modelNamePattern ILIKE :search OR modelRegistry.provider ILIKE :search)',
        { search: `%${queryDto.search}%` }
      );
    }

    // Lọc theo provider (nếu có trong search)
    if (queryDto.search && Object.values(ProviderEnum).includes(queryDto.search.toUpperCase() as ProviderEnum)) {
      query.orWhere('modelRegistry.provider = :provider', {
        provider: queryDto.search.toUpperCase()
      });
    }

    // Sắp xếp
    if (queryDto.sortBy) {
      const direction = queryDto.sortDirection || 'ASC';
      query.orderBy(`modelRegistry.${queryDto.sortBy}`, direction);
    } else {
      query.orderBy('modelRegistry.createdAt', 'DESC');
    }

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Kiểm tra registry có tồn tại không
   * @param id ID của registry
   * @returns True nếu tồn tại
   */
  async isExists(id: string): Promise<boolean> {
    const count = await this.createBaseQuery()
      .andWhere('modelRegistry.id = :id', { id })
      .getCount();

    return count > 0;
  }

  /**
   * Kiểm tra pattern đã tồn tại chưa (cập nhật method cũ)
   * @param pattern Pattern cần kiểm tra
   * @param excludeId ID cần loại trừ (cho update)
   * @returns True nếu đã tồn tại
   */
  async existsByPattern(pattern: string, excludeId?: string): Promise<boolean> {
    const query = this.createQueryBuilder('modelRegistry')
      .where('modelRegistry.modelNamePattern = :pattern', { pattern })
      .andWhere('modelRegistry.deletedAt IS NULL');

    if (excludeId) {
      query.andWhere('modelRegistry.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Tìm các registry đã xóa với phân trang
   * @param queryDto DTO query
   * @returns Kết quả phân trang
   */
  async findDeletedWithPagination(queryDto: any): Promise<PaginatedResult<ModelRegistry>> {
    const query = this.createQueryBuilder('modelRegistry')
      .select([
        'modelRegistry.id',
        'modelRegistry.provider',
        'modelRegistry.modelNamePattern',
        'modelRegistry.inputModalities',
        'modelRegistry.outputModalities',
        'modelRegistry.samplingParameters',
        'modelRegistry.features',
        'modelRegistry.createdAt',
        'modelRegistry.updatedAt',
        'modelRegistry.deletedAt',
        'modelRegistry.createdBy',
        'modelRegistry.updatedBy',
        'modelRegistry.deletedBy'
      ])
      .where('modelRegistry.deletedAt IS NOT NULL');

    // Tìm kiếm theo model name pattern
    if (queryDto.modelNamePattern) {
      query.andWhere('modelRegistry.modelNamePattern ILIKE :pattern', {
        pattern: `%${queryDto.modelNamePattern}%`
      });
    }

    // Sắp xếp theo thời gian xóa
    query.orderBy('modelRegistry.deletedAt', 'DESC');

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Soft delete model registry
   * @param id ID của registry
   * @param deletedBy ID người xóa
   * @returns true nếu thành công
   */
  async softDeleteRegistry(id: string, deletedBy: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(ModelRegistry)
      .set({
        deletedAt: Date.now(),
        deletedBy
      })
      .where('id = :id', { id })
      .andWhere('deletedAt IS NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Khôi phục model registry đã xóa
   * @param id ID của registry
   * @returns true nếu thành công
   */
  async restoreCustom(id: string): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(ModelRegistry)
      .set({
        deletedAt: null,
        deletedBy: null
      })
      .where('id = :id', { id })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Tìm registry theo pattern matching
   * @param modelName Tên model cần match
   * @param provider Provider (optional)
   * @returns Registry phù hợp hoặc null
   */
  async findByPatternMatch(modelName: string, provider?: ProviderEnum): Promise<ModelRegistry | null> {
    const query = this.createBaseQuery();

    if (provider) {
      query.andWhere('modelRegistry.provider = :provider', { provider });
    }

    // Tìm pattern phù hợp (sử dụng LIKE với wildcard)
    query.andWhere('(:modelName LIKE modelRegistry.modelNamePattern OR modelRegistry.modelNamePattern LIKE :pattern)', {
      modelName,
      pattern: `%${modelName}%`
    });

    // Sắp xếp theo độ chính xác (pattern ngắn hơn = chính xác hơn)
    query.orderBy('LENGTH(modelRegistry.modelNamePattern)', 'ASC');

    return query.getOne();
  }

  /**
   * Tìm registry theo ID với đầy đủ thông tin (cho API detail)
   * @param id ID của registry
   * @returns Registry với đầy đủ thông tin hoặc null
   */
  async findByIdWithDetails(id: string): Promise<ModelRegistry | null> {
    return this.createQueryBuilder('modelRegistry')
      .select([
        'modelRegistry.id',
        'modelRegistry.provider',
        'modelRegistry.modelNamePattern',
        'modelRegistry.inputModalities',
        'modelRegistry.outputModalities',
        'modelRegistry.samplingParameters',
        'modelRegistry.features',
        'modelRegistry.basePricing',
        'modelRegistry.fineTunePricing',
        'modelRegistry.trainingPricing',
        'modelRegistry.createdAt',
        'modelRegistry.updatedAt',
        'modelRegistry.createdBy',
        'modelRegistry.updatedBy'
      ])
      .where('modelRegistry.id = :id', { id })
      .andWhere('modelRegistry.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Tạo nhiều model registry cùng lúc
   * @param registries Danh sách registry cần tạo
   * @param createdBy ID người tạo
   * @returns Danh sách registry đã tạo
   */
  async bulkCreate(registries: Partial<ModelRegistry>[], createdBy: number): Promise<ModelRegistry[]> {
    const now = Date.now();

    const entities = registries.map(registry => ({
      ...registry,
      createdAt: now,
      updatedAt: now,
      createdBy,
      updatedBy: createdBy
    }));

    const result = await this.createQueryBuilder()
      .insert()
      .into(ModelRegistry)
      .values(entities)
      .returning('*')
      .execute();

    return result.raw;
  }

  /**
   * Xóa mềm nhiều model registry cùng lúc
   * @param ids Danh sách ID cần xóa
   * @param deletedBy ID người xóa
   * @returns Object chứa danh sách ID thành công và thất bại
   */
  async bulkSoftDelete(ids: string[], deletedBy: number): Promise<{ successIds: string[], failedIds: string[] }> {
    const successIds: string[] = [];
    const failedIds: string[] = [];

    for (const id of ids) {
      try {
        const result = await this.createQueryBuilder()
          .update(ModelRegistry)
          .set({
            deletedAt: Date.now(),
            deletedBy
          })
          .where('id = :id', { id })
          .andWhere('deletedAt IS NULL')
          .execute();

        if ((result.affected || 0) > 0) {
          successIds.push(id);
        } else {
          failedIds.push(id);
        }
      } catch (error) {
        this.logger.error(`Lỗi khi xóa registry ${id}:`, error);
        failedIds.push(id);
      }
    }

    return { successIds, failedIds };
  }

  /**
   * Kiểm tra nhiều pattern có trùng lặp không
   * @param patterns Danh sách pattern cần kiểm tra
   * @returns Danh sách pattern đã tồn tại
   */
  async checkDuplicatePatterns(patterns: string[]): Promise<string[]> {
    const result = await this.createQueryBuilder('modelRegistry')
      .select('modelRegistry.modelNamePattern')
      .where('modelRegistry.modelNamePattern IN (:...patterns)', { patterns })
      .andWhere('modelRegistry.deletedAt IS NULL')
      .getMany();

    return result.map(r => r.modelNamePattern);
  }
}
