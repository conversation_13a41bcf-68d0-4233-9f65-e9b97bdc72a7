import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response';
import { SystemModelsRepository } from '../../repositories/system-models.repository';
import { MODELS_ERROR_CODES } from '../../exceptions/models.exception';
import { AppException } from '@common/exceptions';
import {
  SystemModelsQueryDto,
  SystemModelsResponseDto,
  ToggleActiveResponseDto
} from '../dto/system-models';

/**
 * Service xử lý logic nghiệp vụ cho Admin System Models
 */
@Injectable()
export class AdminSystemModelsService {
  private readonly logger = new Logger(AdminSystemModelsService.name);

  constructor(
    private readonly systemModelsRepository: SystemModelsRepository,
  ) {}

  /**
   * Lấy danh sách system models theo provider với phân trang
   * @param queryDto DTO query
   * @returns <PERSON>ết quả phân trang với thông tin model registry
   */
  async findByProvider(queryDto: SystemModelsQueryDto): Promise<PaginatedResult<SystemModelsResponseDto>> {
    this.logger.log(`Lấy danh sách system models với query: ${JSON.stringify(queryDto)}`);

    try {
      const result = await this.systemModelsRepository.findByProviderWithPagination(queryDto);

      // Chuyển đổi sang DTO response
      const items = result.items.map(item => 
        plainToInstance(SystemModelsResponseDto, item, { excludeExtraneousValues: true })
      );

      return {
        items,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách system models:', error);
      throw new AppException(
        MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED,
        'Không thể lấy danh sách system models'
      );
    }
  }

  /**
   * Bật/tắt trạng thái active của system model
   * @param id ID của system model
   * @returns System model với trạng thái đã cập nhật
   */
  async toggleActive(id: string): Promise<ToggleActiveResponseDto> {
    this.logger.log(`Bật/tắt trạng thái active cho system model ${id}`);

    try {
      // Kiểm tra system model có tồn tại không
      const existingModel = await this.systemModelsRepository.findById(id);
      if (!existingModel) {
        throw new AppException(
          MODELS_ERROR_CODES.SYSTEM_KEY_LLM_NOT_FOUND,
          `Không tìm thấy system model với ID: ${id}`
        );
      }

      // Thực hiện toggle active
      const updatedModel = await this.systemModelsRepository.toggleActive(id);
      if (!updatedModel) {
        throw new AppException(
          MODELS_ERROR_CODES.SYSTEM_KEY_LLM_UPDATE_FAILED,
          'Không thể cập nhật trạng thái system model'
        );
      }

      this.logger.log(`Đã cập nhật trạng thái active cho system model ${id}: ${updatedModel.active}`);

      // Chuyển đổi sang DTO response
      return plainToInstance(ToggleActiveResponseDto, updatedModel, { excludeExtraneousValues: true });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi toggle active system model ${id}:`, error);
      throw new AppException(
        MODELS_ERROR_CODES.SYSTEM_KEY_LLM_UPDATE_FAILED,
        'Không thể cập nhật trạng thái system model'
      );
    }
  }

  // TODO: Thêm các methods khác khi cần thiết
  // - createSystemModel()
  // - updateSystemModel()
  // - deleteSystemModel()
  // - findById()
}
