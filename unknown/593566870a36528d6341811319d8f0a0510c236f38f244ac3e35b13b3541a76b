import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng system_models trong cơ sở dữ liệu
 * Quản lý các model hệ thống
 */
@Entity('system_models')
export class SystemModels {
  /**
   * UUID của system model
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * ID định danh của model
   */
  @Column({
    name: 'model_id',
    type: 'varchar',
    length: 255,
    comment: 'ID định danh của model'
  })
  modelId: string;

  /**
   * Liên kết đến bảng model_registry
   */
  @Column({
    name: 'model_registry_id',
    type: 'uuid',
    comment: 'Liên kết đến bảng model_registry'
  })
  modelRegistryId: string;

  /**
   * Trạng thái hoạt động của model
   */
  @Column({
    name: 'active',
    type: 'boolean',
    default: false,
    comment: 'Trạng thái hoạt động của model'
  })
  active: boolean;
}
