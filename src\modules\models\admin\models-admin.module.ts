import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { EmployeeModule } from '@modules/employee/employee.module';
import { ServicesModule } from '@shared/services/services.module';

// Import entities
import * as entities from '../entities';

// Import repositories
import * as repositories from '../repositories';

// Import controllers
import {
  AdminModelRegistryController,
  AdminDataFineTuneController,
  AdminSystemKeyLlmController,
  AdminSystemModelsController,
} from './controllers';

// Import services
import {
  AdminModelRegistryService,
  AdminDataFineTuneService,
  AdminSystemKeyLlmService,
  AdminSystemModelsService,
} from './services';

// Import helpers
import { ApiKeyEncryptionHelper } from '../helpers/api-key-encryption.helper';

/**
 * Module quản lý models cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.ModelRegistry,
      entities.SystemKeyLlm,
      entities.FineTuneHistories,
      entities.UserKeyLlm,
      entities.UserDataFineTune,
      entities.AdminDataFineTune,
      // New entities
      entities.UserModels,
      entities.UserModelKeyLlm,
      entities.UserModelFineTune,
      entities.SystemModels,
      entities.SystemModelKeyLlm,
    ]),
    ConfigModule,
    HttpModule,
    EmployeeModule,
    ServicesModule,
  ],
  controllers: [
    AdminModelRegistryController,
    AdminDataFineTuneController,
    AdminSystemKeyLlmController,
    AdminSystemModelsController,
  ],
  providers: [
    // Services
    AdminModelRegistryService,
    AdminDataFineTuneService,
    AdminSystemKeyLlmService,
    AdminSystemModelsService,

    // Repositories
    repositories.ModelRegistryRepository,
    repositories.SystemKeyLlmRepository,
    repositories.FineTuneHistoriesRepository,
    repositories.UserKeyLlmRepository,
    repositories.UserDataFineTuneRepository,
    repositories.AdminDataFineTuneRepository,
    repositories.SystemModelsRepository,

    // Helpers
    ApiKeyEncryptionHelper,
  ],
  exports: [
    AdminModelRegistryService,
    AdminDataFineTuneService,
    AdminSystemKeyLlmService,
    AdminSystemModelsService,
  ],
})
export class ModelsAdminModule {}
