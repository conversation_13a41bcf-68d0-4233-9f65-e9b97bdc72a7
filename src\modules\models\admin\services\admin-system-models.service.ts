import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response';
import { SystemModelsRepository } from '../../repositories/system-models.repository';
import { MODELS_ERROR_CODES } from '../../exceptions/models.exception';
import { AppException } from '@common/exceptions';
import {
  SystemModelsQueryDto,
  SystemModelsResponseDto
} from '../dto/system-models';

/**
 * Service xử lý logic nghiệp vụ cho Admin System Models
 */
@Injectable()
export class AdminSystemModelsService {
  private readonly logger = new Logger(AdminSystemModelsService.name);

  constructor(
    private readonly systemModelsRepository: SystemModelsRepository,
  ) {}

  /**
   * Lấy danh sách system models theo provider với phân trang
   * @param queryDto DTO query
   * @returns Kết quả phân trang với thông tin model registry
   */
  async findByProvider(queryDto: SystemModelsQueryDto): Promise<PaginatedResult<SystemModelsResponseDto>> {
    this.logger.log(`Lấy danh sách system models với query: ${JSON.stringify(queryDto)}`);

    try {
      const result = await this.systemModelsRepository.findByProviderWithPagination(queryDto);

      // Chuyển đổi sang DTO response
      const items = result.items.map(item => 
        plainToInstance(SystemModelsResponseDto, item, { excludeExtraneousValues: true })
      );

      return {
        items,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error('Lỗi khi lấy danh sách system models:', error);
      throw new AppException(
        MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED,
        'Không thể lấy danh sách system models'
      );
    }
  }

  // TODO: Thêm các methods khác khi cần thiết
  // - createSystemModel()
  // - updateSystemModel()
  // - deleteSystemModel()
  // - findById()
  // - toggleActive()
}
