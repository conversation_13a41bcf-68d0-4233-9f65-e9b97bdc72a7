import { HttpStatus } from '@nestjs/common';
import { ErrorCode, AppException } from '@common/exceptions';
import { MODELS_ERROR_CODES } from './models.exception';

/**
 * Error codes cho Model Registry (20020-20029)
 * S<PERSON> dụng dải mã từ models.exception.ts, bổ sung thêm các mã mới
 */
export const MODEL_REGISTRY_ERROR_CODES = {
  // Lỗi validation - 20020 (mã mới)
  MODEL_REGISTRY_VALIDATION_ERROR: new ErrorCode(
    20020,
    'Lỗi validation model registry',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi bulk operation - 20021 (mã mới)
  MODEL_REGISTRY_BULK_OPERATION_ERROR: new ErrorCode(
    20021,
    'Lỗi bulk operation model registry',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi bulk create - 20022 (mã mới)
  MODEL_REGISTRY_BULK_CREATE_FAILED: new ErrorCode(
    20022,
    'Tạo nhiều model registry thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi bulk delete - 20023 (mã mới)
  MODEL_REGISTRY_BULK_DELETE_FAILED: new ErrorCode(
    20023,
    'Xóa nhiều model registry thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi update - 20024 (mã mới)
  MODEL_REGISTRY_UPDATE_FAILED: new ErrorCode(
    20024,
    'Cập nhật model registry thất bại',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi pattern trùng lặp trong request - 20025 (mã mới)
  MODEL_REGISTRY_DUPLICATE_PATTERNS_IN_REQUEST: new ErrorCode(
    20025,
    'Có pattern trùng lặp trong danh sách tạo',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi không thể xóa - 20026 (mã mới)
  MODEL_REGISTRY_CANNOT_DELETE: new ErrorCode(
    20026,
    'Không thể xóa model registry',
    HttpStatus.BAD_REQUEST,
  ),
};

/**
 * Class chứa các exception cho Model Registry
 * Sử dụng error codes từ MODELS_ERROR_CODES và MODEL_REGISTRY_ERROR_CODES
 */
export class ModelRegistryException {
  /**
   * Lỗi không tìm thấy model registry
   * Sử dụng mã lỗi từ models.exception.ts
   */
  static notFound(id?: string): AppException {
    return new AppException(
      MODELS_ERROR_CODES.MODEL_REGISTRY_NOT_FOUND,
      id ? `Không tìm thấy model registry với ID: ${id}` : 'Không tìm thấy model registry'
    );
  }

  /**
   * Lỗi pattern đã tồn tại
   * Sử dụng mã lỗi từ models.exception.ts
   */
  static duplicatePattern(pattern: string): AppException {
    return new AppException(
      MODELS_ERROR_CODES.MODEL_REGISTRY_PATTERN_EXISTS,
      `Model name pattern '${pattern}' đã tồn tại trong hệ thống`
    );
  }

  /**
   * Lỗi validation dữ liệu
   */
  static validationError(message: string): AppException {
    return new AppException(
      MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_VALIDATION_ERROR,
      `Lỗi validation: ${message}`
    );
  }

  /**
   * Lỗi bulk operation
   */
  static bulkOperationError(message: string): AppException {
    return new AppException(
      MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_BULK_OPERATION_ERROR,
      `Lỗi bulk operation: ${message}`
    );
  }

  /**
   * Lỗi không thể xóa
   */
  static cannotDelete(reason: string): AppException {
    return new AppException(
      MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_CANNOT_DELETE,
      `Không thể xóa model registry: ${reason}`
    );
  }

  /**
   * Lỗi pattern trùng lặp trong request
   */
  static duplicatePatternsInRequest(): AppException {
    return new AppException(
      MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_DUPLICATE_PATTERNS_IN_REQUEST,
      MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_DUPLICATE_PATTERNS_IN_REQUEST.message
    );
  }

  /**
   * Lỗi bulk create
   */
  static bulkCreateFailed(message?: string): AppException {
    return new AppException(
      MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_BULK_CREATE_FAILED,
      message || MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_BULK_CREATE_FAILED.message
    );
  }

  /**
   * Lỗi update
   */
  static updateFailed(message?: string): AppException {
    return new AppException(
      MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_UPDATE_FAILED,
      message || MODEL_REGISTRY_ERROR_CODES.MODEL_REGISTRY_UPDATE_FAILED.message
    );
  }
}
