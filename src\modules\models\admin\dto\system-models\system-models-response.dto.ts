import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { ProviderEnum } from '../../../constants';
import { 
  InputModalityEnum, 
  OutputModalityEnum, 
  SamplingParameterEnum, 
  FeatureEnum 
} from '../../../constants/model-capabilities.enum';
import { ModelPricingInterface } from '../../../interfaces/pricing.interface';

/**
 * DTO cho response của system model với thông tin model registry
 */
@Exclude()
export class SystemModelsResponseDto {
  /**
   * UUID của system model
   */
  @ApiProperty({
    description: 'UUID của system model',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * ID định danh của model
   */
  @ApiProperty({
    description: 'ID định danh của model',
    example: 'gpt-4-turbo-preview',
  })
  @Expose()
  modelId: string;

  /**
   * Trạng thái hoạt động của model
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của model',
    example: true,
  })
  @Expose()
  active: boolean;

  /**
   * Nhà cung cấp model (từ model registry)
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @Expose()
  provider: ProviderEnum;

  /**
   * Tên mẫu đại diện của model (từ model registry)
   */
  @ApiProperty({
    description: 'Tên mẫu đại diện của model',
    example: 'gpt-4*',
  })
  @Expose()
  modelNamePattern: string;

  /**
   * Các loại dữ liệu đầu vào hỗ trợ (từ model registry)
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
  })
  @Expose()
  inputModalities: InputModalityEnum[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ (từ model registry)
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
  })
  @Expose()
  outputModalities: OutputModalityEnum[];

  /**
   * Các tham số sampling hỗ trợ (từ model registry)
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling hỗ trợ',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
  })
  @Expose()
  samplingParameters: SamplingParameterEnum[];

  /**
   * Các feature đặc biệt hỗ trợ (từ model registry)
   */
  @ApiPropertyOptional({
    description: 'Các feature đặc biệt hỗ trợ',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL],
  })
  @Expose()
  features: FeatureEnum[];

  /**
   * Giá cơ bản cho model (từ model registry)
   */
  @ApiProperty({
    description: 'Giá cơ bản cho model',
    example: { inputRate: 1, outputRate: 2 },
  })
  @Expose()
  basePricing: ModelPricingInterface;

  /**
   * Giá fine-tune cho model (từ model registry)
   */
  @ApiProperty({
    description: 'Giá fine-tune cho model',
    example: { inputRate: 2, outputRate: 4 },
  })
  @Expose()
  fineTunePricing: ModelPricingInterface;

  /**
   * Giá training cho model (từ model registry)
   */
  @ApiProperty({
    description: 'Giá training cho model',
    example: 0,
  })
  @Expose()
  trainingPricing: number;
}
