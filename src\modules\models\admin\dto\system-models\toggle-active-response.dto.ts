import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho response của toggle active system model
 */
@Exclude()
export class ToggleActiveResponseDto {
  /**
   * UUID của system model
   */
  @ApiProperty({
    description: 'UUID của system model',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * ID định danh của model
   */
  @ApiProperty({
    description: 'ID định danh của model',
    example: 'gpt-4-turbo-preview',
  })
  @Expose()
  modelId: string;

  /**
   * Trạng thái hoạt động mới của model
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động mới của model',
    example: true,
  })
  @Expose()
  active: boolean;

  /**
   * Thời gian cập nhật (epoch timestamp)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (epoch timestamp)',
    example: 1703123456789,
  })
  @Expose()
  updatedAt: number;
}
