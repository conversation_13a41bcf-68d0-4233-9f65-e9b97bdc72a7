import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto } from '@common/dto';
import { ProviderEnum } from '../../../constants';

/**
 * DTO cho việc truy vấn danh sách system models
 */
export class SystemModelsQueryDto extends QueryDto {
  /**
   * Lọc theo nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnum)
  provider?: ProviderEnum;

  /**
   * Lọc theo trạng thái hoạt động
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái hoạt động',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  active?: boolean;
}
