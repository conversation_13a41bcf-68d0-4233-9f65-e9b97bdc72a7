import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { SystemModelsResponseDto } from './system-models-response.dto';

/**
 * DTO cho response danh sách system models với phân trang
 */
export class SystemModelsListResponseDto {
  /**
   * Danh sách system models
   */
  @ApiProperty({
    description: 'Danh sách system models',
    type: [SystemModelsResponseDto],
  })
  @Type(() => SystemModelsResponseDto)
  items: SystemModelsResponseDto[];

  /**
   * Thông tin phân trang
   */
  @ApiProperty({
    description: 'Thông tin phân trang',
    example: {
      totalItems: 100,
      itemCount: 10,
      itemsPerPage: 10,
      totalPages: 10,
      currentPage: 1
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
