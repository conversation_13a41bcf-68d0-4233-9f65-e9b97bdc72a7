import {
  <PERSON>,
  Get,
  Patch,
  Param,
  Query,
  UseGuards,
  Logger,
  ParseUUIDPipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam
} from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@common/response';
import { AdminSystemModelsService } from '../services/admin-system-models.service';
import {
  SystemModelsQueryDto,
  SystemModelsListResponseDto,
  ToggleActiveResponseDto
} from '../dto/system-models';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API admin cho System Models
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_SYSTEM_MODELS)
@Controller('admin/system-models')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminSystemModelsController {
  private readonly logger = new Logger(AdminSystemModelsController.name);

  constructor(
    private readonly adminSystemModelsService: AdminSystemModelsService,
  ) {}

  /**
   * API lấy danh sách system models theo provider với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách system models theo provider',
    description: 'Lấy danh sách system models với thông tin đầy đủ từ model registry, có thể lọc theo provider và trạng thái active'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thành công',
    type: SystemModelsListResponseDto,
    schema: {
      example: {
        success: true,
        data: {
          items: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              modelId: 'gpt-4-turbo-preview',
              active: true,
              provider: 'OPENAI',
              inputModalities: ['text', 'image'],
              outputModalities: ['text'],
              samplingParameters: ['temperature', 'top_p'],
              features: ['tool_call'],
              basePricing: { inputRate: 1, outputRate: 2 },
              fineTunePricing: { inputRate: 2, outputRate: 4 },
              trainingPricing: 0
            }
          ],
          meta: {
            totalItems: 100,
            itemCount: 10,
            itemsPerPage: 10,
            totalPages: 10,
            currentPage: 1
          }
        },
        message: 'Lấy danh sách system models thành công'
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Lỗi validation',
    schema: {
      example: {
        success: false,
        errorCode: 20070,
        message: 'Không thể lấy danh sách system models'
      }
    }
  })
  async findByProvider(
    @Query() queryDto: SystemModelsQueryDto,
  ): Promise<ApiResponseDto<SystemModelsListResponseDto>> {
    this.logger.log(`Lấy danh sách system models với query: ${JSON.stringify(queryDto)}`);

    const result = await this.adminSystemModelsService.findByProvider(queryDto);

    return ApiResponseDto.success(result, 'Lấy danh sách system models thành công');
  }

  /**
   * API bật/tắt trạng thái active của system model
   */
  @Patch(':id/toggle-active')
  @ApiOperation({
    summary: 'Bật/tắt trạng thái active của system model',
    description: 'Đảo ngược trạng thái active hiện tại của system model (true -> false hoặc false -> true)'
  })
  @ApiParam({
    name: 'id',
    description: 'UUID của system model',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái thành công',
    type: ToggleActiveResponseDto,
    schema: {
      example: {
        success: true,
        data: {
          id: '123e4567-e89b-12d3-a456-************',
          modelId: 'gpt-4-turbo-preview',
          active: true,
          updatedAt: 1703123456789
        },
        message: 'Cập nhật trạng thái system model thành công'
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Không tìm thấy system model',
    schema: {
      example: {
        success: false,
        errorCode: 20030,
        message: 'Không tìm thấy system model với ID: 123e4567-e89b-12d3-a456-************'
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: 'Lỗi cập nhật',
    schema: {
      example: {
        success: false,
        errorCode: 20036,
        message: 'Không thể cập nhật trạng thái system model'
      }
    }
  })
  async toggleActive(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<ToggleActiveResponseDto>> {
    this.logger.log(`Bật/tắt trạng thái active cho system model ${id}`);

    const result = await this.adminSystemModelsService.toggleActive(id);

    return ApiResponseDto.success(result, 'Cập nhật trạng thái system model thành công');
  }

  // TODO: Thêm các endpoints khác khi cần thiết
  // @Post() - Tạo system model mới
  // @Put(':id') - Cập nhật system model
  // @Delete(':id') - Xóa system model
  // @Get(':id') - Lấy chi tiết system model
}
