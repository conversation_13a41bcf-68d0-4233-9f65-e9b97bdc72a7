import { AppException } from '@common/exceptions';

/**
 * Class chứa các exception cho Model Registry
 */
export class ModelRegistryException {
  /**
   * Lỗi không tìm thấy model registry
   */
  static notFound(id?: string): AppException {
    return new AppException(
      ModelRegistryErrorCode.MODEL_REGISTRY_NOT_FOUND,
      id ? `Không tìm thấy model registry với ID: ${id}` : 'Không tìm thấy model registry'
    );
  }

  /**
   * Lỗi pattern đã tồn tại
   */
  static duplicatePattern(pattern: string): AppException {
    return new AppException(
      ModelRegistryErrorCode.MODEL_REGISTRY_DUPLICATE_PATTERN,
      `Model name pattern '${pattern}' đã tồn tại trong hệ thống`
    );
  }

  /**
   * Lỗi validation dữ liệu
   */
  static validationError(message: string): AppException {
    return new AppException(
      ModelRegistryErrorCode.MODEL_REGISTRY_VALIDATION_ERROR,
      `Lỗi validation: ${message}`
    );
  }

  /**
   * Lỗi bulk operation
   */
  static bulkOperationError(message: string): AppException {
    return new AppException(
      ModelRegistryErrorCode.MODEL_REGISTRY_BULK_OPERATION_ERROR,
      `Lỗi bulk operation: ${message}`
    );
  }

  /**
   * Lỗi không thể xóa
   */
  static cannotDelete(reason: string): AppException {
    return new AppException(
      ModelRegistryErrorCode.MODEL_REGISTRY_CANNOT_DELETE,
      `Không thể xóa model registry: ${reason}`
    );
  }
}
